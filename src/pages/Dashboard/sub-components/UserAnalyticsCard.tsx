import React, { useRef, useEffect, useCallback } from 'react'
import { motion } from 'framer-motion'
import { RefreshCw, TrendingUp } from 'lucide-react'
import * as echarts from 'echarts'
import type { UsersMetrics } from '../../../services/management/managementService'

interface UserAnalyticsCardProps {
  data: UsersMetrics | null
  loading: boolean
  error: string | null
  onRefresh: () => void
  cardVariants: any
}

const UserAnalyticsCard: React.FC<UserAnalyticsCardProps> = ({
  data,
  loading,
  error,
  onRefresh,
  cardVariants
}) => {
  const chartRef = useRef<HTMLDivElement>(null)
  const chartInstance = useRef<echarts.ECharts | null>(null)

  useEffect(() => {
    if (!chartRef.current) return

    if (!chartInstance.current) {
      console.log('UserAnalyticsCard: Initializing chart', {
        container: chartRef.current,
        containerSize: {
          width: chartRef.current?.offsetWidth,
          height: chartRef.current?.offsetHeight
        }
      })

      chartInstance.current = echarts.init(chartRef.current)

      const handleResize = () => {
        chartInstance.current?.resize()
      }
      window.addEventListener('resize', handleResize)

      return () => {
        window.removeEventListener('resize', handleResize)
        chartInstance.current?.dispose()
        chartInstance.current = null
      }
    }
  }, [])

  const runRaceChart = useCallback((dates: string[], regData: number[], activeData: number[]) => {
    if (!chartInstance.current) return

    console.log('UserAnalyticsCard: Running race chart with:', { dates, regData, activeData })

    const option = {
      // Race animation - key for the racing effect
      animationDuration: 4000, // 4 seconds for full race
      animationEasing: 'cubicOut' as const,
      title: {
        text: 'User Trends Race',
        textStyle: {
          color: '#374151',
          fontSize: 16,
          fontWeight: 'bold'
        },
        left: 'center',
        top: 10
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#e5e7eb',
        borderWidth: 1,
        textStyle: { color: '#374151', fontSize: 12 },
        formatter: (params: any) => {
          let tooltip = `<div style="padding: 8px;">
            <div style="font-weight: bold; margin-bottom: 8px;">${params[0].name}</div>`

          params.forEach((param: any) => {
            tooltip += `
              <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 4px;">
                <div style="width: 10px; height: 10px; background: ${param.color}; border-radius: 50%;"></div>
                <span>${param.seriesName}: <strong>${param.value}</strong></span>
              </div>`
          })

          tooltip += '</div>'
          return tooltip
        }
      },
      legend: {
        data: ['New Registrations', 'Active Users'],
        top: 40,
        left: 'center',
        textStyle: {
          color: '#6b7280',
          fontSize: 12
        }
      },
      xAxis: {
        type: 'category',
        data: dates,
        boundaryGap: false,
        axisLine: {
          lineStyle: { color: '#e5e7eb', width: 1 }
        },
        axisTick: { show: false },
        axisLabel: {
          color: '#6b7280',
          fontSize: 11,
          margin: 12
        },
        splitLine: { show: false }
      },
      yAxis: {
        type: 'value',
        name: 'Count',
        nameTextStyle: {
          color: '#6b7280',
          fontSize: 11
        },
        axisLine: { show: false },
        axisTick: { show: false },
        axisLabel: {
          color: '#6b7280',
          fontSize: 11
        },
        splitLine: {
          lineStyle: {
            color: '#f3f4f6',
            width: 1,
            type: 'dashed'
          }
        }
      },
      grid: {
        right: 60,
        left: 60,
        top: 80,
        bottom: 60,
        containLabel: true
      },
      series: [
        {
          name: 'New Registrations',
          data: regData,
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 8,
          showSymbol: true,
          lineStyle: {
            width: 4,
            color: '#10b981'
          },
          itemStyle: {
            color: '#10b981',
            borderColor: '#ffffff',
            borderWidth: 2
          },
          areaStyle: {
            color: 'rgba(16, 185, 129, 0.2)'
          },
          emphasis: {
            focus: 'series'
          },
          // Progressive drawing animation (race effect)
          animationDuration: 3000,
          animationEasing: 'cubicOut' as const,
          animationDelay: 0,
          animationDurationUpdate: 1000,
          animationDelayUpdate: function (idx: number) {
            return idx * 100 // Progressive reveal from left to right
          }
        },
        {
          name: 'Active Users',
          data: activeData,
          type: 'line',
          smooth: true,
          symbol: 'diamond',
          symbolSize: 8,
          showSymbol: true,
          lineStyle: {
            width: 4,
            color: '#8b5cf6'
          },
          itemStyle: {
            color: '#8b5cf6',
            borderColor: '#ffffff',
            borderWidth: 2
          },
          areaStyle: {
            color: 'rgba(139, 92, 246, 0.2)'
          },
          emphasis: {
            focus: 'series'
          },
          // Progressive drawing animation with delay (racing effect)
          animationDuration: 3000,
          animationEasing: 'cubicOut' as const,
          animationDelay: 500, // Start after first series for racing effect
          animationDurationUpdate: 1000,
          animationDelayUpdate: function (idx: number) {
            return idx * 100 + 200 // Progressive reveal with offset
          }
        }
      ]
    }

    chartInstance.current.setOption(option, true)

    setTimeout(() => {
      chartInstance.current?.resize()
    }, 100)

    console.log('UserAnalyticsCard: Race chart updated successfully')
  }, [])

  const updateChart = useCallback(() => {
    if (!data?.daily_data || !chartInstance.current) {
      console.log('UserAnalyticsCard: Missing data or chart instance', {
        hasData: !!data?.daily_data,
        hasChart: !!chartInstance.current,
        data: data
      })
      return
    }

    console.log('UserAnalyticsCard: Processing data for race chart:', data.daily_data)

    const registrations = data.daily_data.registrations || []
    const activeUsers = data.daily_data.active_users || []

    const allDates = new Set([
      ...registrations.map(item => item._id),
      ...activeUsers.map(item => item._id)
    ])
    const sortedDates = Array.from(allDates).sort()

    if (sortedDates.length === 0) {
      console.log('UserAnalyticsCard: No dates found in data, showing test chart')
      // Show a test chart with sample data
      runRaceChart(
        ['Jul 7', 'Jul 10', 'Jul 14'],
        [1, 1, 1],
        [0, 0, 4]
      )
      return
    }

    // Format dates for display
    const formattedDates = sortedDates.map(date =>
      new Date(date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
    )

    // Create data maps for easy lookup
    const regMap = new Map(registrations.map(item => [item._id, item.count]))
    const activeMap = new Map(activeUsers.map(item => [item._id, item.count]))

    // Fill data arrays
    const regData = sortedDates.map(date => regMap.get(date) || 0)
    const activeData = sortedDates.map(date => activeMap.get(date) || 0)

    console.log('UserAnalyticsCard: Chart data prepared:', {
      dates: formattedDates,
      registrations: regData,
      activeUsers: activeData
    })

    // Run the race chart
    runRaceChart(formattedDates, regData, activeData)
  }, [data, runRaceChart])

  useEffect(() => {
    if (data?.daily_data && chartInstance.current) {
      updateChart()
    }
  }, [data, updateChart])

  if (loading) {
    return (
      <motion.div
        variants={cardVariants}
        className="bg-card border border-border rounded-xl p-6 relative overflow-hidden h-full"
      >
        <div className="animate-pulse space-y-6">
          <div className="flex items-center justify-between">
            <div className="h-6 bg-muted rounded w-32" />
            <div className="h-8 w-8 bg-muted rounded" />
          </div>
          <div className="h-96 bg-muted rounded animate-pulse" />
        </div>
      </motion.div>
    )
  }

  if (error) {
    return (
      <motion.div
        variants={cardVariants}
        className="bg-card border border-destructive/20 rounded-xl p-6 h-full"
      >
        <div className="text-center space-y-4">
          <div className="text-destructive font-medium">Failed to load user analytics</div>
          <p className="text-sm text-muted-foreground">{error}</p>
          <motion.button
            onClick={onRefresh}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
          >
            <RefreshCw className="h-4 w-4 inline mr-2" />
            Retry
          </motion.button>
        </div>
      </motion.div>
    )
  }

  if (!data) {
    return (
      <motion.div
        variants={cardVariants}
        className="bg-card border border-border rounded-xl p-6 h-full"
      >
        <div className="text-center text-muted-foreground">
          No user analytics data available
          <div className="mt-2">
            <motion.button
              onClick={onRefresh}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors text-sm"
            >
              <RefreshCw className="h-4 w-4 inline mr-2" />
              Load Data
            </motion.button>
          </div>
        </div>
      </motion.div>
    )
  }

  return (
    <motion.div
      variants={cardVariants}
      className="bg-gradient-to-br from-white/90 via-blue-50/60 to-purple-50/70 dark:from-gray-900/90 dark:via-blue-900/30 dark:to-purple-900/40 border border-blue-200/60 dark:border-blue-800/40 rounded-2xl p-6 relative overflow-hidden h-full shadow-xl backdrop-blur-sm"
    >
      <div className="absolute inset-0 bg-gradient-to-br from-blue-100/30 via-purple-50/20 to-indigo-100/30 dark:from-blue-900/20 dark:via-purple-900/10 dark:to-indigo-900/20 rounded-2xl animate-pulse" style={{ animationDuration: '4s' }} />

      <div className="relative space-y-6 h-full flex flex-col">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-blue-600" />
              User Trends Race
            </h3>
            <p className="text-sm text-muted-foreground">Animated racing line chart</p>
          </div>
          <div className="flex items-center gap-2">
            <motion.button
              onClick={() => {
                if (chartInstance.current && data?.daily_data) {
                  updateChart()
                }
              }}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              className="p-2 hover:bg-muted rounded-lg transition-colors group"
              title="Replay race animation"
            >
              <TrendingUp className="h-4 w-4 text-blue-600" />
            </motion.button>
            <motion.button
              onClick={onRefresh}
              whileHover={{ scale: 1.1, rotate: 180 }}
              whileTap={{ scale: 0.9 }}
              className="p-2 hover:bg-muted rounded-lg transition-colors group"
              title="Refresh data"
            >
              <RefreshCw className="h-4 w-4 text-muted-foreground group-hover:rotate-180 transition-transform duration-500" />
            </motion.button>
          </div>
        </div>

        <div className="flex-1 min-h-0" style={{ minHeight: '400px' }}>
          <div
            ref={chartRef}
            style={{ height: '100%', width: '100%' }}
          />
        </div>
      </div>
    </motion.div>
  )
}

export default UserAnalyticsCard
