import React, { useRef, useEffect } from 'react'
import { motion } from 'framer-motion'
import { RefreshCw, TrendingUp, Users } from 'lucide-react'
import * as echarts from 'echarts'
import type { UsersMetrics } from '../../../services/management/managementService'

interface UserAnalyticsCardProps {
  data: UsersMetrics | null
  loading: boolean
  error: string | null
  onRefresh: () => void
  cardVariants: any
}

/**
 * User Analytics Card - Clean race line chart for daily user trends
 * Displays registration and active user data from daily_data API response
 */
const UserAnalyticsCard: React.FC<UserAnalyticsCardProps> = ({
  data,
  loading,
  error,
  onRefresh,
  cardVariants
}) => {
  const chartRef = useRef<HTMLDivElement>(null)
  const chartInstance = useRef<echarts.ECharts | null>(null)

  // Initialize and update chart
  useEffect(() => {
    if (!chartRef.current) return

    // Initialize chart if not exists
    if (!chartInstance.current) {
      chartInstance.current = echarts.init(chartRef.current)

      // Handle resize
      const handleResize = () => {
        chartInstance.current?.resize()
      }
      window.addEventListener('resize', handleResize)

      // Cleanup on unmount
      return () => {
        window.removeEventListener('resize', handleResize)
        chartInstance.current?.dispose()
        chartInstance.current = null
      }
    }

    // Update chart with data
    if (data?.daily_data && chartInstance.current) {
      updateChart()
    }
  }, [data])

  const updateChart = () => {
    if (!data?.daily_data || !chartInstance.current) return

    // Prepare data from API response
    const registrations = data.daily_data.registrations || []
    const activeUsers = data.daily_data.active_users || []

    // Get all unique dates and sort them
    const allDates = new Set([
      ...registrations.map(item => item._id),
      ...activeUsers.map(item => item._id)
    ])
    const sortedDates = Array.from(allDates).sort()

    // Format dates for display
    const formattedDates = sortedDates.map(date =>
      new Date(date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
    )

    // Create data maps for easy lookup
    const regMap = new Map(registrations.map(item => [item._id, item.count]))
    const activeMap = new Map(activeUsers.map(item => [item._id, item.count]))

    // Fill data arrays
    const regData = sortedDates.map(date => regMap.get(date) || 0)
    const activeData = sortedDates.map(date => activeMap.get(date) || 0)

    // Chart configuration
    const option = {
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#e5e7eb',
        borderWidth: 1,
        textStyle: { color: '#374151', fontSize: 12 },
        formatter: (params: any) => {
          let tooltip = `<div style="padding: 8px;">
            <div style="font-weight: bold; margin-bottom: 8px;">${params[0].name}</div>`

          params.forEach((param: any) => {
            tooltip += `
              <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 4px;">
                <div style="width: 10px; height: 10px; background: ${param.color}; border-radius: 50%;"></div>
                <span>${param.seriesName}: <strong>${param.value}</strong></span>
              </div>`
          })

          tooltip += '</div>'
          return tooltip
        }
      },
        legend: {
          data: ['New Registrations', 'Active Users'],
          top: 10,
          textStyle: {
            color: '#6b7280',
            fontSize: 12
          }
        },
        grid: {
          left: '5%',
          right: '5%',
          bottom: '15%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: formattedDates,
          axisLine: {
            show: true,
            lineStyle: { color: '#e5e7eb', width: 1 }
          },
          axisTick: { show: false },
          axisLabel: {
            color: '#6b7280',
            fontSize: 11,
            interval: 0,
            rotate: 45,
            margin: 10
          }
        },
        yAxis: {
          type: 'value',
          axisLine: { show: false },
          axisTick: { show: false },
          axisLabel: { color: '#6b7280', fontSize: 11 },
          splitLine: {
            lineStyle: {
              color: '#f3f4f6',
              type: 'dashed'
            }
          }
        },
        series: [
          {
            name: 'New Registrations',
            data: registrationCounts,
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 8,
            showSymbol: true,
            lineStyle: {
              width: 4,
              color: {
                type: 'linear',
                x: 0, y: 0, x2: 1, y2: 0,
                colorStops: [
                  { offset: 0, color: '#10b981' },
                  { offset: 0.5, color: '#34d399' },
                  { offset: 1, color: '#6ee7b7' }
                ]
              },
              shadowColor: 'rgba(16, 185, 129, 0.3)',
              shadowBlur: 10,
              shadowOffsetY: 3
            },
            itemStyle: {
              color: '#10b981',
              borderColor: '#ffffff',
              borderWidth: 3,
              shadowColor: 'rgba(16, 185, 129, 0.5)',
              shadowBlur: 8
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0, y: 0, x2: 0, y2: 1,
                colorStops: [
                  { offset: 0, color: 'rgba(16, 185, 129, 0.3)' },
                  { offset: 0.5, color: 'rgba(16, 185, 129, 0.15)' },
                  { offset: 1, color: 'rgba(16, 185, 129, 0.05)' }
                ]
              }
            },
            emphasis: {
              focus: 'series',
              itemStyle: {
                shadowBlur: 20,
                shadowColor: 'rgba(16, 185, 129, 0.8)'
              }
            },
            animationDuration: 2500,
            animationEasing: 'cubicOut',
            animationDelay: function (idx: number) {
              return idx * 80
            }
          },
          {
            name: 'Active Users',
            data: activeUserCounts,
            type: 'line',
            smooth: true,
            symbol: 'diamond',
            symbolSize: 8,
            showSymbol: true,
            lineStyle: {
              width: 4,
              color: {
                type: 'linear',
                x: 0, y: 0, x2: 1, y2: 0,
                colorStops: [
                  { offset: 0, color: '#8b5cf6' },
                  { offset: 0.5, color: '#a78bfa' },
                  { offset: 1, color: '#c4b5fd' }
                ]
              },
              shadowColor: 'rgba(139, 92, 246, 0.3)',
              shadowBlur: 10,
              shadowOffsetY: 3
            },
            itemStyle: {
              color: '#8b5cf6',
              borderColor: '#ffffff',
              borderWidth: 3,
              shadowColor: 'rgba(139, 92, 246, 0.5)',
              shadowBlur: 8
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0, y: 0, x2: 0, y2: 1,
                colorStops: [
                  { offset: 0, color: 'rgba(139, 92, 246, 0.3)' },
                  { offset: 0.5, color: 'rgba(139, 92, 246, 0.15)' },
                  { offset: 1, color: 'rgba(139, 92, 246, 0.05)' }
                ]
              }
            },
            emphasis: {
              focus: 'series',
              itemStyle: {
                shadowBlur: 20,
                shadowColor: 'rgba(139, 92, 246, 0.8)'
              }
            },
            animationDuration: 2500,
            animationEasing: 'cubicOut',
            animationDelay: function (idx: number) {
              return idx * 80 + 600
            }
          }
        ],
        dataZoom: [
          {
            type: 'inside',
            start: 0,
            end: 100
          },
          {
            type: 'slider',
            show: formattedDates.length > 7,
            start: 0,
            end: 100,
            height: 20,
            bottom: 5,
            textStyle: {
              color: '#6b7280',
              fontSize: 10
            },
            borderColor: '#e5e7eb',
            fillerColor: 'rgba(16, 185, 129, 0.2)',
            handleStyle: {
              color: '#10b981',
              borderColor: '#ffffff'
            }
          }
        ],
        animation: true,
        animationThreshold: 2000,
        animationDuration: 1000,
        animationDelay: (idx: number) => idx * 100,
        animationDurationUpdate: 300,
        animationDelayUpdate: (idx: number) => idx * 50
      }

      // Update line race chart
      lineRaceChartInstance.current.setOption(lineRaceChartOption, true)

      // Force resize and refresh after setting option
      setTimeout(() => {
        if (lineRaceChartInstance.current) {
          lineRaceChartInstance.current.resize()
        }
      }, 50)

      console.log('Chart updated successfully')
    } catch (error) {
      console.error('Error updating line race chart:', error)
    }
  }, [data])

  if (loading) {
    return (
      <motion.div
        variants={cardVariants}
        className="bg-card border border-border rounded-xl p-6 relative overflow-hidden"
      >
        <div className="animate-pulse space-y-6">
          <div className="flex items-center justify-between">
            <div className="h-6 bg-muted rounded w-32" />
            <div className="h-8 w-8 bg-muted rounded" />
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div className="h-20 bg-muted rounded animate-pulse" />
            <div className="h-20 bg-muted rounded animate-pulse" />
          </div>
          <div className="h-96 bg-muted rounded animate-pulse" />
        </div>
      </motion.div>
    )
  }

  if (error) {
    console.error('UserAnalyticsCard error:', error)
    return (
      <motion.div
        variants={cardVariants}
        className="bg-card border border-destructive/20 rounded-xl p-6"
      >
        <div className="text-center space-y-4">
          <div className="text-destructive font-medium">Failed to load user analytics</div>
          <p className="text-sm text-muted-foreground">{error}</p>
          <motion.button
            onClick={onRefresh}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
          >
            <RefreshCw className="h-4 w-4 inline mr-2" />
            Retry
          </motion.button>
        </div>
      </motion.div>
    )
  }

  if (!data) {
    console.log('UserAnalyticsCard: No data available')
    return (
      <motion.div
        variants={cardVariants}
        className="bg-card border border-border rounded-xl p-6"
      >
        <div className="text-center text-muted-foreground">
          No user analytics data available
          <div className="mt-2">
            <motion.button
              onClick={onRefresh}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors text-sm"
            >
              <RefreshCw className="h-4 w-4 inline mr-2" />
              Load Data
            </motion.button>
          </div>
        </div>
      </motion.div>
    )
  }

  return (
    <motion.div
      variants={cardVariants}
      className="bg-card border border-border rounded-xl p-4 h-full"
    >
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-foreground">User Trends</h3>
          <motion.button
            onClick={onRefresh}
            whileHover={{ scale: 1.1, rotate: 180 }}
            whileTap={{ scale: 0.9 }}
            className="p-2 hover:bg-muted rounded-md transition-colors"
            title="Refresh analytics"
          >
            <RefreshCw className="h-4 w-4 text-muted-foreground" />
          </motion.button>
        </div>

        {/* Line Race Chart - Full height with better background */}
        <div className="flex-1 min-h-0 bg-gradient-to-br from-blue-50/50 via-purple-50/30 to-indigo-50/50 dark:from-blue-900/20 dark:via-purple-900/15 dark:to-indigo-900/20 rounded-xl p-4 border border-blue-100/50 dark:border-blue-800/30 relative">
          <div
            ref={lineRaceChartRef}
            style={{ height: '100%', width: '100%', minHeight: '350px' }}
          />
          {/* Debug info */}
          {data && (
            <div className="absolute bottom-2 left-2 text-xs text-muted-foreground bg-background/80 px-2 py-1 rounded">
              Data: {data.daily_data?.registrations?.length || 0} registrations, {data.daily_data?.active_users?.length || 0} active users
            </div>
          )}
        </div>
      </div>
    </motion.div>
  )
}

export default UserAnalyticsCard
