import React, { useRef, useEffect, useCallback } from 'react'
import { motion } from 'framer-motion'
import { RefreshCw, TrendingUp } from 'lucide-react'
import * as echarts from 'echarts'
import type { UsersMetrics } from '../../../services/management/managementService'

interface UserAnalyticsCardProps {
  data: UsersMetrics | null
  loading: boolean
  error: string | null
  onRefresh: () => void
  cardVariants: any
}

const UserAnalyticsCard: React.FC<UserAnalyticsCardProps> = ({
  data,
  loading,
  error,
  onRefresh,
  cardVariants
}) => {
  const chartRef = useRef<HTMLDivElement>(null)
  const chartInstance = useRef<echarts.ECharts | null>(null)

  // Initialize chart
  useEffect(() => {
    if (!chartRef.current) return

    if (!chartInstance.current) {
      chartInstance.current = echarts.init(chartRef.current)
      
      const handleResize = () => {
        chartInstance.current?.resize()
      }
      window.addEventListener('resize', handleResize)
      
      return () => {
        window.removeEventListener('resize', handleResize)
        chartInstance.current?.dispose()
        chartInstance.current = null
      }
    }
  }, [])

  const updateChart = useCallback(() => {
    if (!data?.daily_data || !chartInstance.current) {
      console.log('UserAnalyticsCard: Missing data or chart instance')
      return
    }

    console.log('UserAnalyticsCard: Updating chart with data:', data.daily_data)

    const registrations = data.daily_data.registrations || []
    const activeUsers = data.daily_data.active_users || []

    const allDates = new Set([
      ...registrations.map(item => item._id),
      ...activeUsers.map(item => item._id)
    ])
    const sortedDates = Array.from(allDates).sort()

    if (sortedDates.length === 0) {
      console.log('UserAnalyticsCard: No dates found in data')
      return
    }

    const formattedDates = sortedDates.map(date =>
      new Date(date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
    )

    const regMap = new Map(registrations.map(item => [item._id, item.count]))
    const activeMap = new Map(activeUsers.map(item => [item._id, item.count]))

    const regData = sortedDates.map(date => regMap.get(date) || 0)
    const activeData = sortedDates.map(date => activeMap.get(date) || 0)

    const option = {
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#e5e7eb',
        borderWidth: 1,
        textStyle: { color: '#374151', fontSize: 12 }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        top: '15%',
        containLabel: true
      },
      legend: {
        data: ['New Registrations', 'Active Users'],
        top: '5%',
        left: 'center'
      },
      xAxis: {
        type: 'category',
        data: formattedDates,
        boundaryGap: false
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: 'New Registrations',
          data: regData,
          type: 'line',
          smooth: true,
          lineStyle: { color: '#10b981', width: 3 },
          itemStyle: { color: '#10b981' },
          areaStyle: { color: 'rgba(16, 185, 129, 0.2)' }
        },
        {
          name: 'Active Users',
          data: activeData,
          type: 'line',
          smooth: true,
          lineStyle: { color: '#8b5cf6', width: 3 },
          itemStyle: { color: '#8b5cf6' },
          areaStyle: { color: 'rgba(139, 92, 246, 0.2)' }
        }
      ]
    }

    chartInstance.current.setOption(option, true)
    
    setTimeout(() => {
      chartInstance.current?.resize()
    }, 100)

    console.log('UserAnalyticsCard: Chart updated successfully')
  }, [data])

  useEffect(() => {
    if (data?.daily_data && chartInstance.current) {
      updateChart()
    }
  }, [data, updateChart])

  if (loading) {
    return (
      <motion.div
        variants={cardVariants}
        className="bg-card border border-border rounded-xl p-6 relative overflow-hidden h-full"
      >
        <div className="animate-pulse space-y-6">
          <div className="flex items-center justify-between">
            <div className="h-6 bg-muted rounded w-32" />
            <div className="h-8 w-8 bg-muted rounded" />
          </div>
          <div className="h-96 bg-muted rounded animate-pulse" />
        </div>
      </motion.div>
    )
  }

  if (error) {
    return (
      <motion.div
        variants={cardVariants}
        className="bg-card border border-destructive/20 rounded-xl p-6 h-full"
      >
        <div className="text-center space-y-4">
          <div className="text-destructive font-medium">Failed to load user analytics</div>
          <p className="text-sm text-muted-foreground">{error}</p>
          <motion.button
            onClick={onRefresh}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
          >
            <RefreshCw className="h-4 w-4 inline mr-2" />
            Retry
          </motion.button>
        </div>
      </motion.div>
    )
  }

  if (!data) {
    return (
      <motion.div
        variants={cardVariants}
        className="bg-card border border-border rounded-xl p-6 h-full"
      >
        <div className="text-center text-muted-foreground">
          No user analytics data available
          <div className="mt-2">
            <motion.button
              onClick={onRefresh}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors text-sm"
            >
              <RefreshCw className="h-4 w-4 inline mr-2" />
              Load Data
            </motion.button>
          </div>
        </div>
      </motion.div>
    )
  }

  return (
    <motion.div
      variants={cardVariants}
      className="bg-gradient-to-br from-white/90 via-blue-50/60 to-purple-50/70 dark:from-gray-900/90 dark:via-blue-900/30 dark:to-purple-900/40 border border-blue-200/60 dark:border-blue-800/40 rounded-2xl p-6 relative overflow-hidden h-full shadow-xl backdrop-blur-sm"
    >
      <div className="absolute inset-0 bg-gradient-to-br from-blue-100/30 via-purple-50/20 to-indigo-100/30 dark:from-blue-900/20 dark:via-purple-900/10 dark:to-indigo-900/20 rounded-2xl animate-pulse" style={{ animationDuration: '4s' }} />

      <div className="relative space-y-6 h-full flex flex-col">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-blue-600" />
              User Trends
            </h3>
            <p className="text-sm text-muted-foreground">Daily registration and activity trends</p>
          </div>
          <motion.button
            onClick={onRefresh}
            whileHover={{ scale: 1.1, rotate: 180 }}
            whileTap={{ scale: 0.9 }}
            className="p-2 hover:bg-muted rounded-lg transition-colors group"
            title="Refresh analytics"
          >
            <RefreshCw className="h-4 w-4 text-muted-foreground group-hover:rotate-180 transition-transform duration-500" />
          </motion.button>
        </div>

        <div className="flex-1 min-h-0" style={{ minHeight: '400px' }}>
          <div
            ref={chartRef}
            style={{ height: '100%', width: '100%' }}
          />
        </div>
      </div>
    </motion.div>
  )
}

export default UserAnalyticsCard
